"""
Twitch client for chat monitoring, API integration, and OAuth handling.
"""

import asyncio
import aiohttp
from typing import Optional, Dict, Any, List
from datetime import datetime

import twitchio
from twitchio.ext import commands

from ..config.settings import TwitchConfig
from ..core.events import EventBus, Event, EventType, create_system_event
from ..utils.logger import get_logger
from ..utils.security import SecurityManager

logger = get_logger(__name__)


class TwitchClient:
    """
    Twitch client that handles chat monitoring, API integration, and OAuth.
    """
    
    def __init__(self, config: TwitchConfig, event_bus: EventBus):
        """
        Initialize the Twitch client.
        
        Args:
            config: Twitch configuration
            event_bus: Event bus for communication
        """
        self.config = config
        self.event_bus = event_bus
        self.security_manager = SecurityManager()
        
        # TwitchIO bot instance
        self.bot: Optional[twitchio.Bot] = None
        
        # API session
        self.session: Optional[aiohttp.ClientSession] = None
        
        # State tracking
        self._initialized = False
        self._running = False
        self._connected_channels: List[str] = []
        
        # OAuth tokens
        self._access_token: Optional[str] = None
        self._refresh_token: Optional[str] = None
        
    async def initialize(self) -> None:
        """Initialize the Twitch client."""
        try:
            logger.info("Initializing Twitch client...")
            
            # Validate configuration
            if not self.config.client_id or not self.config.client_secret:
                raise ValueError("Twitch client ID and secret are required")
            
            # Initialize HTTP session
            self.session = aiohttp.ClientSession()
            
            # Load stored tokens if available
            await self._load_tokens()
            
            # Initialize TwitchIO bot if we have tokens
            if self._access_token:
                await self._initialize_bot()
            else:
                logger.warning("No Twitch tokens available, OAuth flow required")
            
            self._initialized = True
            logger.info("Twitch client initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize Twitch client: {e}")
            raise
    
    async def start(self) -> None:
        """Start the Twitch client."""
        if not self._initialized:
            raise RuntimeError("Twitch client not initialized")
        
        try:
            logger.info("Starting Twitch client...")
            
            if self.bot:
                # Start the bot in a background task
                asyncio.create_task(self.bot.start())
                self._running = True
                
                # Emit started event
                await self.event_bus.emit(create_system_event(
                    EventType.TWITCH_CONNECTED,
                    {"client_id": self.config.client_id}
                ))
                
                logger.info("Twitch client started successfully")
            else:
                logger.warning("Twitch bot not available, skipping start")
                
        except Exception as e:
            logger.error(f"Failed to start Twitch client: {e}")
            raise
    
    async def stop(self) -> None:
        """Stop the Twitch client."""
        try:
            logger.info("Stopping Twitch client...")
            
            if self.bot:
                await self.bot.close()
            
            if self.session:
                await self.session.close()
            
            self._running = False
            
            # Emit stopped event
            await self.event_bus.emit(create_system_event(
                EventType.TWITCH_DISCONNECTED,
                {}
            ))
            
            logger.info("Twitch client stopped successfully")
            
        except Exception as e:
            logger.error(f"Error stopping Twitch client: {e}")
    
    async def _initialize_bot(self) -> None:
        """Initialize the TwitchIO bot."""
        try:
            # Create bot instance
            self.bot = commands.Bot(
                token=self._access_token,
                client_id=self.config.client_id,
                nick=self.config.bot_username or "HarmonyBot",
                prefix="!",
                initial_channels=[self.config.streamer_username] if self.config.streamer_username else []
            )
            
            # Set up event handlers
            self._setup_bot_events()
            
            logger.info("TwitchIO bot initialized")
            
        except Exception as e:
            logger.error(f"Failed to initialize TwitchIO bot: {e}")
            raise
    
    def _setup_bot_events(self) -> None:
        """Set up TwitchIO bot event handlers."""
        if not self.bot:
            return
        
        @self.bot.event()
        async def event_ready():
            """Called when the bot is ready."""
            logger.info(f"Twitch bot ready as {self.bot.nick}")
            
        @self.bot.event()
        async def event_message(message):
            """Handle incoming chat messages."""
            if message.echo:
                return
            
            # Emit chat message event
            await self.event_bus.emit(Event(
                type=EventType.CHAT_MESSAGE,
                data={
                    "username": message.author.name,
                    "message": message.content,
                    "channel": message.channel.name,
                    "timestamp": datetime.now().isoformat(),
                    "user_id": message.author.id,
                    "is_mod": message.author.is_mod,
                    "is_subscriber": message.author.is_subscriber
                },
                source="twitch_client",
                timestamp=datetime.now()
            ))
    
    async def _load_tokens(self) -> None:
        """Load stored OAuth tokens."""
        try:
            # Try to load from config first
            if self.config.bot_token:
                self._access_token = self.config.bot_token
                logger.info("Loaded Twitch token from configuration")
            elif self.config.streamer_token:
                self._access_token = self.config.streamer_token
                logger.info("Loaded streamer token from configuration")
            else:
                logger.info("No Twitch tokens found in configuration")
                
        except Exception as e:
            logger.error(f"Error loading Twitch tokens: {e}")
    
    async def send_message(self, channel: str, message: str) -> None:
        """
        Send a message to a Twitch channel.
        
        Args:
            channel: Channel name to send message to
            message: Message content
        """
        if not self.bot or not self._running:
            logger.warning("Cannot send message: Twitch bot not running")
            return
        
        try:
            channel_obj = self.bot.get_channel(channel)
            if channel_obj:
                await channel_obj.send(message)
                logger.debug(f"Sent message to {channel}: {message}")
            else:
                logger.warning(f"Channel {channel} not found")
                
        except Exception as e:
            logger.error(f"Failed to send message to {channel}: {e}")
    
    async def get_channel_info(self, channel: str) -> Optional[Dict[str, Any]]:
        """
        Get information about a Twitch channel.
        
        Args:
            channel: Channel name
            
        Returns:
            Channel information or None if not found
        """
        if not self.session or not self._access_token:
            logger.warning("Cannot get channel info: No session or token")
            return None
        
        try:
            headers = {
                "Authorization": f"Bearer {self._access_token}",
                "Client-Id": self.config.client_id
            }
            
            async with self.session.get(
                f"https://api.twitch.tv/helix/channels?broadcaster_login={channel}",
                headers=headers
            ) as response:
                if response.status == 200:
                    data = await response.json()
                    return data.get("data", [{}])[0] if data.get("data") else None
                else:
                    logger.error(f"Failed to get channel info: {response.status}")
                    return None
                    
        except Exception as e:
            logger.error(f"Error getting channel info for {channel}: {e}")
            return None
    
    @property
    def is_connected(self) -> bool:
        """Check if the client is connected to Twitch."""
        return self._running and self.bot is not None
    
    @property
    def connected_channels(self) -> List[str]:
        """Get list of connected channels."""
        return self._connected_channels.copy()
